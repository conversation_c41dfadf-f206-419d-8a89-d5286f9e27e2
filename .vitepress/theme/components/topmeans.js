import {useUserStore} from './UserCenter/userStore';
import log from 'loglevel';

// 导入服务模块
import {apiService, formManager, mapService, markdownService, scrollManager, styleManager} from './services';

// 导入环境变量
const BACKEND_SRV_URL = import.meta.env.VITE_BACKEND_SRV_URL;
const TRAVEL_PLAN_PROMPT_PREFIX = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_PREFIX;
const TRAVEL_PLAN_PROMPT_SUFFIX = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_SUFFIX;
const TRAVEL_PLAN_PROMPT_WEATHER = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_WEATHER;
const TRAVEL_PLAN_PROMPT_RENT = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_RENT;
const TRAVEL_PLAN_PROMPT_DRIVING = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_DRIVING;
const TRAVEL_PLAN_PROMPT_VIEW = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_VIEW_ONLY;
const TRAVEL_PLAN_PROMPT_FOOD = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_FOOD;
const TRAVEL_PLAN_PROMPT_HOTEL = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_HOTEL;
const TRAVEL_PLAN_PROMPT_COST = import.meta.env.VITE_TRAVEL_PLAN_PROMPT_COST;
log.setLevel('info');

export default {
    data() {
        return {
            contents: [],
            showBtn: true,
            loading: false,
            errorMessage: '',
            last_start: '',
            last_end: '',
            rent_requirements: '',
            rent_customized: false,
            plan_requirements: '0',  // 默认选择速度优先
            plan_customized: false,
            hotel_requirements: '',
            hotel_customized: false,
            // 地图最大化状态
            maximizedMapIndex: -1,  // -1表示没有地图被最大化
            originalParent: null,   // 保存地图原始父容器
            originalNextSibling: null,  // 保存地图原始位置
            // 路线规划选项
            planOptions: [
                { value: '0', text: '速度优先(默认值)' },
                { value: '1', text: '费用优先' },
                { value: '2', text: '距离优先' },
                { value: '3', text: '不走高速且避免收费' },
                { value: '4', text: '躲避拥堵' },
                { value: '5', text: '不走高速' },
                { value: '6', text: '躲避拥堵且不走高速' },
                { value: '7', text: '躲避拥堵且距离优先' },
                { value: '8', text: '躲避拥堵且不走高速且距离优先' },
                { value: '9', text: '躲避拥堵且不走高速且费用优先' }
            ],
            // 表单数据
            ...formManager.getDefaultFormData(),
            // 天数导航相关
            selectedDayIndex: -1,  // -1表示显示所有天，其他值表示显示特定天
            // 新增：天数详情面板相关状态
            activeDayDetailIndex: -1,  // -1表示没有展开详情面板，其他值表示展开的天数
            expandedSectionType: null,  // 当前展开的内容类型：weather, rent, driving, view, food, hotel
            // 全局加载状态
            loadingStates: {
                weather: false,
                rent: false,
                driving: false,
                view: false,
                food: false,
                hotel: false,
                cost: false
            },
            // 完成提示窗口
            showCompletionModal: false,
            // 失败提示窗口
            showFailureModal: false,
            failureReason: '',
            // 悬浮滚动按钮
            showFloatingButtons: false
        }
    },
    computed: {
        // 获取当前选中的路线规划策略文本
        selectedPlanText() {
            const selected = this.planOptions.find(option => option.value === this.plan_requirements);
            return selected ? selected.text : '';
        },
        // 是否有任何加载中的内容
        isAnyLoading() {
            return Object.values(this.loadingStates).some(state => state);
        },
        // 当前加载消息
        currentLoadingMessage() {
            // 找到当前正在处理的天数
            const currentDay = this.getCurrentProcessingDay();
            const messages = {
                weather: '正在查询天气信息，请稍等...',
                rent: '正在思考租车方案，请稍等...',
                driving: `正在规划第${currentDay}天的路线，请稍等...`,
                view: `正在规划第${currentDay}天的景点信息，请稍等...`,
                food: `正在规划第${currentDay}天的美食信息，请稍等...`,
                hotel: `正在规划第${currentDay}天的住宿信息，请稍等...`,
                cost: '正在计算费用预算，请稍等...'
            };

            for (const [type, isLoading] of Object.entries(this.loadingStates)) {
                if (isLoading) {
                    return messages[type];
                }
            }
            return '正在处理中，请稍等...';
        },
        // 当前进度步骤
        currentProgressStep() {
            // 步骤1：基本信息填写阶段
            if (this.showBtn && this.contents.length === 0) {
                return 1;
            }

            // 步骤2：生成规划阶段（正在规划中）
            if (!this.showBtn || (this.contents.length > 0 && !this.isAllPlanningCompleted())) {
                return 2;
            }

            // 步骤3：完成阶段
            if (this.contents.length > 0 && this.isAllPlanningCompleted()) {
                return 3;
            }

            // 默认返回步骤1
            return 1;
        }
    },
    async mounted() {
        // 确保只在客户端执行
        if (typeof window === 'undefined') return;

        try {
            // 初始化地图服务
            await mapService.initialize();

            // 设置地址自动完成
            mapService.setupAutoComplete(
                "start-tipinput",
                "end-tipinput",
                (address, locationInfo) => {
                    this.s_address = address;
                    this.s_location = locationInfo || this.getDefaultFormData().s_location;
                },
                (address, locationInfo) => {
                    this.e_address = address;
                    this.e_location = locationInfo || this.getDefaultFormData().e_location;
                }
            );

            // 确保高德地图提示框样式正确应用
            styleManager.ensureAmapSuggestStyles();
        } catch (err) {
            this.errorMessage = err.message;
            log.error('地图初始化失败:', err);
        }

        // 初始化滚动监听器
        scrollManager.initScrollListener();

        // 设置内容完成检查函数
        scrollManager.setContentCompletedChecker(() => this.showBtn);

        // 设置当前显示状态检查函数，帮助滚动管理器了解哪些容器应该可见
        scrollManager.setDisplayStateChecker(() => ({
            selectedDayIndex: this.selectedDayIndex,
            isAnyLoading: this.isAnyLoading
        }));

        // 恢复表单数据
        this.loadFormData();

        // 初始化悬浮滚动按钮
        this.initFloatingButtons();

        // 监听主题变化以更新自定义地址建议样式
        this.initThemeObserver();

        // 尝试恢复规划内容
        const hasRestoredContent = this.loadPlanningContents();
        if (hasRestoredContent) {
            this.showBtn = false;
            // 如果恢复了规划内容，需要在DOM渲染后进行完整的恢复处理
            this.$nextTick(() => {
                // 延迟执行以确保DOM完全渲染
                setTimeout(() => {
                    this.handleContentRestoration();
                }, 500);
            });
        }
    },
    beforeUnmount() {
        // 如果有地图正在最大化状态，恢复它
        if (this.maximizedMapIndex !== -1) {
            this.restoreMapToOriginalPosition(this.maximizedMapIndex);
        }

        // 清理所有服务
        scrollManager.cleanup();
        styleManager.cleanup();
        mapService.cleanup();
        apiService.cleanup();

        // 清理键盘监听器
        this.removeKeyboardListener();

        // 清理悬浮按钮滚动监听器
        if (this.handleScroll && typeof window !== 'undefined') {
            window.removeEventListener('scroll', this.handleScroll);
        }

        // 清理主题观察器
        if (this.themeObserver) {
            this.themeObserver.disconnect();
        }
    },
    watch: {
        contents: {
            handler(newVal) {
                if (newVal.length > 0 && scrollManager.getScrollState().autoScrollEnabled) {
                    this.$nextTick(() => {
                        // 只有在有内容正在加载时才自动滚动
                        if (this.isAnyLoading) {
                            scrollManager.smartScrollToContent();
                        }
                    });
                }

                // 自动保存规划内容到本地存储
                if (newVal.length > 0) {
                    this.savePlanningContents();
                }
            },
            deep: true
        },
        // 监听表单数据变化，自动保存到本地存储
        s_address: {
            handler() {
                this.saveFormData();
            }
        },
        e_address: {
            handler() {
                this.saveFormData();
            }
        },
        s_location: {
            handler() {
                this.saveFormData();
            },
            deep: true
        },
        e_location: {
            handler() {
                this.saveFormData();
            },
            deep: true
        },
        startDate: {
            handler() {
                this.saveFormData();
            }
        },
        dates: {
            handler() {
                this.saveFormData();
            }
        },
        plan_mode: {
            handler() {
                this.saveFormData();
            }
        },
        travel_mode: {
            handler() {
                this.saveFormData();
            }
        },
        // 监听规划相关状态变化，自动保存
        selectedDayIndex: {
            handler() {
                if (this.contents.length > 0) {
                    this.savePlanningContents();
                }
            }
        },
        // 监听新增的状态变量
        activeDayDetailIndex: {
            handler() {
                if (this.contents.length > 0) {
                    this.savePlanningContents();
                }
            }
        },
        expandedSectionType: {
            handler() {
                if (this.contents.length > 0) {
                    this.savePlanningContents();
                }
            }
        },
        showBtn: {
            handler() {
                if (this.contents.length > 0) {
                    this.savePlanningContents();
                }
            }
        }
    },
    methods: {
        parseMarkdown(text) {
            return markdownService.parse(text || '');
        },
        // 获取当前正在处理的天数
        getCurrentProcessingDay() {
            if (!this.contents || this.contents.length === 0) {
                return 1;
            }

            // 找到最后一个有进度的天数
            for (let i = this.contents.length - 1; i >= 0; i--) {
                const content = this.contents[i];
                // 如果有任何状态不是0（未开始），说明这一天正在或已经处理
                if (content.weatherCompleted > 0 ||
                    content.rentCompleted > 0 ||
                    content.drivingCompleted > 0 ||
                    content.viewCompleted > 0 ||
                    content.foodCompleted > 0 ||
                    content.hotelCompleted > 0 ||
                    content.costCompleted > 0) {
                    return i + 1; // 返回1-based的天数
                }
            }

            // 如果所有天数都没有开始，返回第1天
            return 1;
        },
        // 天数导航相关方法
        selectDay(index) {
            // 添加安全检查
            if (index < 0 || index >= this.contents.length || !this.contents[index]) {
                return;
            }

            if (this.isDayCompleted(this.contents[index])) {
                // 新逻辑：点击天数按钮展开详情面板，而不是直接展开内容
                this.activeDayDetailIndex = this.activeDayDetailIndex === index ? -1 : index;

                // 如果展开了详情面板，重置内容展开状态
                if (this.activeDayDetailIndex !== -1) {
                    this.expandedSectionType = null;
                    this.selectedDayIndex = -1;
                }

                // 等待DOM更新后重新渲染可见的地图
                this.$nextTick(() => {
                    this.refreshVisibleMaps();
                });
            }
        },

        // 新增：处理详情面板中的按钮点击
        handleDetailPanelClick(sectionType) {
            const dayIndex = this.activeDayDetailIndex;
            if (dayIndex === -1 || dayIndex >= this.contents.length || dayIndex < 0) return;

            // 确保contents[dayIndex]存在
            if (!this.contents[dayIndex]) return;

            // 设置展开的内容类型
            this.expandedSectionType = sectionType;
            this.selectedDayIndex = dayIndex;

            // 等待DOM更新后滚动到对应区域
            this.$nextTick(() => {
                this.scrollToSection(sectionType, dayIndex);
                this.refreshVisibleMaps();
            });
        },

        // 新增：滚动到指定的内容区域
        scrollToSection(sectionType, dayIndex) {
            let selector = '';

            switch (sectionType) {
                case 'weather':
                    selector = '.weather-header';
                    break;
                case 'rent':
                    selector = '.rent-header';
                    break;
                case 'driving':
                    selector = '.driving-header';
                    break;
                case 'view':
                    selector = '.view-header';
                    break;
                case 'food':
                    selector = '.food-header';
                    break;
                case 'hotel':
                    selector = '.hotel-header';
                    break;
            }

            if (selector) {
                const element = document.querySelector(selector);
                if (element) {
                    setTimeout(() => {
                        scrollManager.scrollToElement(element, 'smooth');
                    }, 100);
                }
            }
        },

        // 新增：检查某个内容类型是否应该显示
        shouldShowSection(sectionType, dayIndex) {
            // 如果没有展开任何内容，只显示header
            if (this.expandedSectionType === null) {
                return false;
            }

            // 检查dayIndex的有效性
            if (dayIndex >= this.contents.length || dayIndex < 0) {
                return false;
            }

            // 如果展开了特定内容，只显示对应的内容
            return this.expandedSectionType === sectionType &&
                   (this.selectedDayIndex === -1 || this.selectedDayIndex === dayIndex);
        },

        // 新增：检查某个天数的内容类型是否可用
        isSectionAvailable(sectionType, dayIndex) {
            if (dayIndex >= this.contents.length || dayIndex < 0) return false;

            const content = this.contents[dayIndex];
            // 添加content存在性检查
            if (!content) return false;

            switch (sectionType) {
                case 'weather':
                    return dayIndex === 0 && content.weatherCompleted === 2;
                case 'rent':
                    return dayIndex === 0 && this.travel_mode === '租车' && content.rentCompleted === 2;
                case 'driving':
                    return content.drivingCompleted === 2;
                case 'view':
                    return content.viewCompleted === 2;
                case 'food':
                    return content.foodCompleted === 2;
                case 'hotel':
                    return content.hotelCompleted === 2;
                default:
                    return false;
            }
        },

        // 新增：处理section header的点击事件
        handleSectionHeaderClick(sectionType, dayIndex) {
            // 如果当前已经展开了这个section，则收起
            if (this.expandedSectionType === sectionType && this.selectedDayIndex === dayIndex) {
                this.expandedSectionType = null;
                this.selectedDayIndex = -1;
            } else {
                // 否则展开这个section
                this.expandedSectionType = sectionType;
                this.selectedDayIndex = dayIndex;

                // 同时设置详情面板状态
                this.activeDayDetailIndex = dayIndex;

                // 等待DOM更新后滚动到对应区域
                this.$nextTick(() => {
                    this.refreshVisibleMaps();
                });
            }
        },

        // 新增：收起所有展开的内容
        collapseAll() {
            this.expandedSectionType = null;
            this.selectedDayIndex = -1;
            this.activeDayDetailIndex = -1;

            this.$nextTick(() => {
                this.refreshVisibleMaps();
            });
        },

        // 刷新所有可见地图的渲染
        refreshVisibleMaps() {
            if (typeof window === 'undefined') return;

            // 遍历所有内容，检查哪些地图应该可见
            this.contents.forEach((content, index) => {
                // 检查地图是否应该显示
                const shouldShowMap = 'amap' in content &&
                    (this.selectedDayIndex === -1 || this.selectedDayIndex === index);

                if (shouldShowMap) {
                    // 检查地图容器是否存在且可见
                    const mapContainer = document.querySelector(`#map-container-${index}`);
                    if (mapContainer && mapContainer.offsetParent !== null) {
                        // 延迟强制重新渲染地图
                        setTimeout(() => {
                            this.forceMapResize(index);
                        }, 100);
                    }
                }
            });
        },
        isDayCompleted(content) {
            // 添加content存在性检查
            if (!content) return false;

            // 至少要有一个板块已完成
            return content.weatherCompleted === 2 ||
                content.rentCompleted === 2 ||
                content.drivingCompleted === 2 ||
                content.viewCompleted === 2 ||
                content.foodCompleted === 2 ||
                content.hotelCompleted === 2 ||
                content.costCompleted === 2;
        },
        getDayTitle(content, index) {
            // 根据起点终点生成天数标题
            if (index === 0) {
                return `${this.s_address} → ${this.e_address}`;
            } else if (index === this.contents.length - 1 && this.plan_mode === '往返') {
                return `${this.e_address} → ${this.s_address}`;
            } else {
                return `${this.e_address}游览`;
            }
        },
        // 加载状态管理
        setLoadingState(type, isLoading) {
            this.loadingStates[type] = isLoading;
        },

        // 重置所有加载状态
        resetAllLoadingStates() {
            this.loadingStates = {
                weather: false,
                rent: false,
                driving: false,
                view: false,
                food: false,
                hotel: false,
                cost: false
            };
        },
        // 保留原方法名以兼容其他调用
        scrollPageToBottom() {
            scrollManager.smartScrollToContent();
        },

        // 重置滚动状态（在开始新一轮操作时调用）
        resetScrollState() {
            scrollManager.resetScrollState();
        },

        // 保存表单数据到本地存储
        saveFormData() {
            const formData = {
                s_address: this.s_address,
                e_address: this.e_address,
                startDate: this.startDate,
                dates: this.dates,
                plan_mode: this.plan_mode,
                travel_mode: this.travel_mode,
                s_location: this.s_location,
                e_location: this.e_location
            };
            formManager.saveFormData(formData);
        },

        // 从本地存储加载表单数据
        loadFormData() {
            const savedData = formManager.loadFormData();
            if (savedData) {
                const mergedData = formManager.mergeFormData({
                    s_address: this.s_address,
                    e_address: this.e_address,
                    startDate: this.startDate,
                    dates: this.dates,
                    plan_mode: this.plan_mode,
                    travel_mode: this.travel_mode,
                    s_location: this.s_location,
                    e_location: this.e_location
                }, savedData);

                // 更新组件数据
                Object.assign(this, mergedData);
            }
        },

        // 清除本地存储的表单数据
        clearFormData() {
            formManager.clearFormData();
        },

        // 重置表单到默认值
        resetFormData() {
            const defaultData = formManager.resetFormData();
            Object.assign(this, defaultData);

            // 同时清除规划内容
            this.clearPlanningContents();
            this.contents = [];
            this.selectedDayIndex = -1;
            // 重置新增的状态变量
            this.activeDayDetailIndex = -1;
            this.expandedSectionType = null;
            this.showBtn = true;
        },

        // 保存规划内容到本地存储
        savePlanningContents() {
            if (typeof window === 'undefined') return;

            try {
                const planningData = {
                    contents: this.contents,
                    last_start: this.last_start,
                    last_end: this.last_end,
                    selectedDayIndex: this.selectedDayIndex,
                    // 保存新增的状态变量
                    activeDayDetailIndex: this.activeDayDetailIndex,
                    expandedSectionType: this.expandedSectionType,
                    rent_requirements: this.rent_requirements,
                    rent_customized: this.rent_customized,
                    plan_requirements: this.plan_requirements,
                    plan_customized: this.plan_customized,
                    hotel_requirements: this.hotel_requirements,
                    hotel_customized: this.hotel_customized,
                    showBtn: this.showBtn,
                    // 保存表单数据，确保恢复时有完整的上下文
                    formData: {
                        s_address: this.s_address,
                        e_address: this.e_address,
                        startDate: this.startDate,
                        dates: this.dates,
                        plan_mode: this.plan_mode,
                        travel_mode: this.travel_mode,
                        s_location: this.s_location,
                        e_location: this.e_location
                    },
                    timestamp: Date.now() // 添加时间戳
                };

                localStorage.setItem('topmeans_planning_contents', JSON.stringify(planningData));
            } catch (error) {
                console.warn('保存规划内容失败:', error);
            }
        },

        // 从本地存储加载规划内容
        loadPlanningContents() {
            if (typeof window === 'undefined') return false;

            try {
                const savedData = localStorage.getItem('topmeans_planning_contents');
                if (!savedData) {
                    return false;
                }

                const planningData = JSON.parse(savedData);

                // 检查数据有效性（设置过期时间，比如24小时）
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                if (planningData.timestamp && (Date.now() - planningData.timestamp > maxAge)) {
                    this.clearPlanningContents();
                    return false;
                }

                // 恢复规划数据
                this.contents = planningData.contents || [];
                this.last_start = planningData.last_start || '';
                this.last_end = planningData.last_end || '';
                this.selectedDayIndex = planningData.selectedDayIndex !== undefined ? planningData.selectedDayIndex : -1;
                // 恢复新增的状态变量
                this.activeDayDetailIndex = planningData.activeDayDetailIndex !== undefined ? planningData.activeDayDetailIndex : -1;
                this.expandedSectionType = planningData.expandedSectionType || null;
                this.rent_requirements = planningData.rent_requirements || '';
                this.rent_customized = planningData.rent_customized || false;
                this.plan_requirements = planningData.plan_requirements || '0';
                this.plan_customized = planningData.plan_customized || false;
                this.hotel_requirements = planningData.hotel_requirements || '';
                this.hotel_customized = planningData.hotel_customized || false;
                this.showBtn = planningData.showBtn !== undefined ? planningData.showBtn : true;

                // 恢复表单数据（如果存在）
                if (planningData.formData) {
                    this.s_address = planningData.formData.s_address || this.s_address;
                    this.e_address = planningData.formData.e_address || this.e_address;
                    this.startDate = planningData.formData.startDate || this.startDate;
                    this.dates = planningData.formData.dates || this.dates;
                    this.plan_mode = planningData.formData.plan_mode || this.plan_mode;
                    this.travel_mode = planningData.formData.travel_mode || this.travel_mode;
                    this.s_location = planningData.formData.s_location || this.s_location;
                    this.e_location = planningData.formData.e_location || this.e_location;
                }

                return true;
            } catch (error) {
                console.warn('加载规划内容失败:', error);
                // 如果数据损坏，清除它
                this.clearPlanningContents();
                return false;
            }
        },

        // 清除本地存储的规划内容
        clearPlanningContents() {
            if (typeof window === 'undefined') return;

            try {
                localStorage.removeItem('topmeans_planning_contents');
            } catch (error) {
                console.warn('清除规划内容失败:', error);
            }
        },

        // 检查是否有已保存的规划内容
        hasSavedPlanningContents() {
            if (typeof window === 'undefined') return false;

            try {
                const savedData = localStorage.getItem('topmeans_planning_contents');
                if (!savedData) return false;

                const planningData = JSON.parse(savedData);

                // 检查数据是否过期
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                if (planningData.timestamp && (Date.now() - planningData.timestamp > maxAge)) {
                    return false;
                }

                // 检查是否有实际的规划内容
                return planningData.contents && planningData.contents.length > 0;
            } catch (error) {
                return false;
            }
        },

        // 更新租车需求输入框的值
        updateRentRequirements(event) {
            this.rent_requirements = event.target.value;
        },

        updateHotelRequirements(event) {
            this.hotel_requirements = event.target.value;
        },

        // 切换地图大小（最大化/缩小）
        toggleMapSize(index) {
            if (this.maximizedMapIndex === index) {
                // 当前地图已最大化，恢复到原始大小
                this.restoreMapToOriginalPosition(index);
                this.maximizedMapIndex = -1;
                this.removeKeyboardListener();
            } else {
                // 最大化指定索引的地图
                this.moveMapToBodyForMaximize(index);
                this.maximizedMapIndex = index;
                this.addKeyboardListener();
            }

            // 地图大小改变后，需要重新计算地图尺寸
            this.$nextTick(() => {
                // 延迟执行以确保CSS动画和DOM移动完成
                setTimeout(() => {
                    this.forceMapResize(index);
                }, 350); // 稍微大于CSS transition时间
            });
        },

        // 将地图移动到body下以实现真正的全屏最大化
        moveMapToBodyForMaximize(index) {
            if (typeof window === 'undefined') return;

            const mapWrapper = document.querySelector(`#map-container-${index}`).closest('.map-wrapper');
            if (mapWrapper) {
                // 保存原始位置信息
                this.originalParent = mapWrapper.parentNode;
                this.originalNextSibling = mapWrapper.nextSibling;

                // 将地图包装器移动到body下
                document.body.appendChild(mapWrapper);
            }
        },

        // 恢复地图到原始位置
        restoreMapToOriginalPosition(index) {
            if (typeof window === 'undefined') return;

            const mapWrapper = document.querySelector(`#map-container-${index}`).closest('.map-wrapper');
            if (mapWrapper && this.originalParent) {
                // 恢复到原始位置
                if (this.originalNextSibling) {
                    this.originalParent.insertBefore(mapWrapper, this.originalNextSibling);
                } else {
                    this.originalParent.appendChild(mapWrapper);
                }

                // 清理保存的引用
                this.originalParent = null;
                this.originalNextSibling = null;
            }
        },

        // 强制重新渲染地图以适应新的容器尺寸
        forceMapResize(index) {
            try {
                const mapInstance = mapService.getMapInstance(index);
                if (!mapInstance) {
                    return;
                }

                // 方法1: 调用高德地图的resize方法
                if (typeof mapInstance.resize === 'function') {
                    mapInstance.resize();
                }

                // 方法2: 获取当前地图状态并重新设置
                const center = mapInstance.getCenter();
                const zoom = mapInstance.getZoom();

                // 方法3: 强制重新计算地图容器尺寸
                if (typeof mapInstance.getSize === 'function') {
                    mapInstance.getSize();
                }

                if (typeof mapInstance.setFitView === 'function') {
                    mapInstance.setFitView();
                }

                // 方法4: 重新设置地图中心和缩放级别（强制刷新）
                setTimeout(() => {
                    try {
                        if (center && zoom && typeof mapInstance.setZoomAndCenter === 'function') {
                            mapInstance.setZoomAndCenter(zoom, center);
                        }

                        // 如果还是有问题，尝试轻微改变缩放级别然后恢复
                        setTimeout(() => {
                            try {
                                if (typeof mapInstance.getZoom === 'function' && typeof mapInstance.setZoom === 'function') {
                                    const currentZoom = mapInstance.getZoom();
                                    mapInstance.setZoom(currentZoom + 0.01);
                                    setTimeout(() => {
                                        mapInstance.setZoom(currentZoom);
                                    }, 50);
                                }
                            } catch (innerErr) {
                                console.warn(`地图 ${index} 缩放调整时出现错误:`, innerErr);
                            }
                        }, 100);
                    } catch (centerErr) {
                        console.warn(`地图 ${index} 中心点设置时出现错误:`, centerErr);
                    }
                }, 100);

            } catch (err) {
                console.warn(`地图 ${index} 重新渲染时出现错误:`, err);
            }
        },

        // 添加键盘监听器（ESC键关闭最大化）
        addKeyboardListener() {
            if (typeof window !== 'undefined') {
                this.escKeyHandler = (event) => {
                    if (event.key === 'Escape' && this.maximizedMapIndex !== -1) {
                        this.toggleMapSize(this.maximizedMapIndex);
                    }
                };

                this.resizeHandler = () => {
                    if (this.maximizedMapIndex !== -1) {
                        // 窗口尺寸改变时，重新调整地图
                        clearTimeout(this.resizeTimeout);
                        this.resizeTimeout = setTimeout(() => {
                            this.forceMapResize(this.maximizedMapIndex);
                        }, 300);
                    }
                };

                window.addEventListener('keydown', this.escKeyHandler);
                window.addEventListener('resize', this.resizeHandler);
            }
        },

        // 移除键盘监听器
        removeKeyboardListener() {
            if (typeof window !== 'undefined') {
                if (this.escKeyHandler) {
                    window.removeEventListener('keydown', this.escKeyHandler);
                    this.escKeyHandler = null;
                }

                if (this.resizeHandler) {
                    window.removeEventListener('resize', this.resizeHandler);
                    this.resizeHandler = null;
                }

                if (this.resizeTimeout) {
                    clearTimeout(this.resizeTimeout);
                    this.resizeTimeout = null;
                }
            }
        },

        // 处理租车需求输入框的回车键事件
        handleRentCustomize(index) {
            if (this.rent_requirements && this.rent_requirements.trim()) {
                this.handleActionClick('rent', index);
            }
        },
        async drivingPlanning(index, start, end, policy, circleLocations) {
            try {
                // 确保地图容器已渲染
                await this.$nextTick();
                await this.$nextTick(); // 双重确保

                await mapService.drivingPlanning(index, start, end, policy, circleLocations);

                // 地图规划完成后，等待一段时间确保渲染完成
                await new Promise(resolve => setTimeout(resolve, 2000));
            } catch (err) {
                log.error('路线生成错误', err);
                this.errorMessage = err.message || '路线生成失败';
            } finally {
                this.loading = false;
            }
        },
        // 存库
        async savePlanToDB(index, account, formattedDateTime) {
            // 保存截图
            await this.saveMapAsImage(index, account, formattedDateTime);

            let md_content = `# Smart Travel Plan\n\n## ${this.s_address} 到 ${this.e_address}\n\n`;
            if (this.contents[index]['weather']) {
                md_content += `${this.contents[index]['weather']}\n\n`;
            }
            if (this.contents[index]['rent']) {
                md_content += `${this.contents[index]['rent']}\n\n`;
            }
            if (this.contents[index]['driving']) {
                md_content += `${this.contents[index]['driving']}\n\n`;
                md_content += `![路线规划](./map-${formattedDateTime}-${index}.png)\n\n`;
            }

            if (this.contents[index]['view']) {
                for (let i = 0; i < this.contents[index]['view'].length; i++) {
                    md_content += `## ${this.contents[index]['view'][i]['name']}\n\n`;
                    if (this.contents[index]['view'][i]['url']) {
                        md_content += `![${this.contents[index]['view'][i]['name']}](${this.contents[index]['view'][i]['url']})\n\n`;
                    }
                    md_content += `${this.contents[index]['view'][i]['info']}\n\n`;
                }
            }

            if (this.contents[index]['food']) {
                for (let i = 0; i < this.contents[index]['food'].length; i++) {
                    md_content += `## ${this.contents[index]['food'][i]['name']}\n\n`;
                    if (this.contents[index]['food'][i]['url']) {
                        md_content += `![${this.contents[index]['food'][i]['name']}](${this.contents[index]['food'][i]['url']})\n\n`;
                    }
                    md_content += `${this.contents[index]['food'][i]['info']}\n\n`;
                }
            }

            if (this.contents[index]['hotel']) {
                for (let i = 0; i < this.contents[index]['hotel'].length; i++) {
                    if (this.contents[index]['hotel'][i]['url']) {
                        md_content += `## [携程直达：${this.contents[index]['hotel'][i]['name']}](${this.contents[index]['hotel'][i]['url']} "${this.contents[index]['hotel'][i]['name']}")\n\n`;
                    } else {
                        md_content += `## ${this.contents[index]['hotel'][i]['name']}\n\n`;
                    }
                    md_content += `${this.contents[index]['hotel'][i]['info']}\n\n`;
                }
            }

            // md_content += `${this.contents[index]['cost']}\n`;

            let response = await fetch(`${BACKEND_SRV_URL}/api/save_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: md_content,
                    user: account,
                    filename: `plan-${formattedDateTime}-${index}.md`, // 文件名
                })
            });
            if (!response.ok) {
                log.error('保存计划失败，请检查网络连接', response);
                throw new Error('保存计划失败，请检查网络连接');
            }

            // 存库
            response = await fetch(`${BACKEND_SRV_URL}/api/user/add_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    account: account,
                    create_time: formattedDateTime,
                    days: this.dates,
                })
            });
            if (!response.ok) {
                log.error('计划存库失败，请检查网络连接', response);
                throw new Error('计划存库失败，请检查网络连接');
            }
        },
        // 保存地图为图片
        async saveMapAsImage(index, account, formattedDateTime) {
            try {
                await mapService.saveMapAsImage(index, account, formattedDateTime);
            } catch (err) {
                log.error('保存地图为图片失败:', err);
                throw err;
            }
        },
        async getHotelUrl(hotelName, account, formattedDateTime, day) {
            try {
                return await apiService.getHotelUrl(hotelName, account, formattedDateTime, day, '');
            } catch (err) {
                log.error('酒店信息获取失败:', err);
                throw err;
            }
        },
        async getFoodImgUrl(foodName, foodInfo) {
            try {
                return await apiService.getFoodImgUrl(foodName, foodInfo);
            } catch (err) {
                log.error('美食图片获取失败:', err);
                throw err;
            }
        },
        async getViewUrl(viewName) {
            try {
                return await apiService.getViewUrl(viewName);
            } catch (err) {
                log.error('景点信息获取失败:', err);
                throw err;
            }
        },
        async askDeepSeek(index, msg, type) {
            const us = useUserStore();
            const { user } = await us.getUserInfo();
            const formattedDateTime = this.getFormattedDate();
            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/ds`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ msg })
                });
                if (!response.ok) {
                    log.error(`DS API 请求失败:${response.statusText}`);
                    throw new Error('DS API 请求失败!');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let result = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    // 处理流式数据
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim());

                    for (const line of lines) {
                        try {
                            if (!line.startsWith('data: ')) continue;

                            const jsonStr = line.slice(6);
                            if (jsonStr === '[DONE]') break;

                            const data = JSON.parse(jsonStr);
                            const msg = data.choices[0].delta.content;

                            if (msg) {
                                result += msg;
                            }

                            if (type === 'rent') {
                                this.contents[index]['rent'] = `**租车建议**\n${result}`;
                            } else if (type === 'plan') {
                                this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n${result}`;
                            }
                        } catch (err) {
                            log.error('解析数据失败:', err);
                        }
                    }
                }

                if (result && type === 'hotel') {
                    const ho_parts = result.split('住宿推荐');
                    for (let i = 0; i < ho_parts.length; i++) {
                        if (i === 0) continue; // 跳过第一个部分
                        if (i > this.contents[index]['hotel'].length) this.contents[index]['hotel'].push({});
                        this.contents[index]['hotel'][i - 1]['info'] = '**酒店信息:** \n';
                        if (ho_parts[i].includes('**酒店信息:**')) {
                            this.contents[index]['hotel'][i - 1]['info'] += ho_parts[i].split('**酒店信息:**')[1].trim()
                            this.contents[index]['hotel'][i - 1]['name'] = ho_parts[i].split('@@')[1].split('$$')[0].trim();
                            try {
                                this.contents[index]['hotel'][i - 1]['url'] = await this.getHotelUrl(this.contents[index]['hotel'][i - 1]['name'], user.account, formattedDateTime, index + 1);
                            } catch (err) { }
                        }
                    }
                }
            } catch (err) {
                log.error('DS API 请求失败:', err);
                throw err;
            }
        },
        async askDS(index, prompt) {
            let result = '';
            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/ds`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ msg: prompt })
                });
                if (!response.ok) {
                    log.error('DS API 请求失败!');
                    throw new Error('DS API 请求失败!');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim());

                    for (const line of lines) {
                        try {
                            if (!line.startsWith('data: ')) continue;

                            const jsonStr = line.slice(6);
                            if (jsonStr === '[DONE]') break;

                            const data = JSON.parse(jsonStr);
                            const msg = data.choices[0].delta.content;

                            if (msg) {
                                result += msg;
                            }

                            this.contents[index]['think'] = result
                                .replace(/%SP/g, '')
                                .replace(/%SC/g, '')
                                .replace(/%EP/g, '')
                                .replace(/%EC/g, '')
                                .replace(/%CIR/g, '')
                                .replace(/@/g, '')
                                .replace(/#/g, '')
                                .replace(/\$/g, '')
                                .replace(/\^/g, '')
                                .replace(/\*/g, '')
                                .replace(/%/g, '')
                                .replace(/&/g, '')
                                .trim();
                        } catch (err) { }
                    }
                }
            } catch (err) {
                log.error(`DS API 请求失败:${err}`);
                throw err;
            }

            return result;
        },
        // 处理操作按钮点击事件
        async handleActionClick(type, index) {
            let processed = false;
            const us = useUserStore();
            const { user } = await us.getUserInfo();
            const formattedDateTime = this.getFormattedDate();
            // 前置 prompt 准备
            let promptTemplate = TRAVEL_PLAN_PROMPT_PREFIX;
            promptTemplate = promptTemplate.replace(/startDate/g, this.startDate)
                .replace(/s_address/g, this.s_address)
                .replace(/e_address/g, this.e_address)
                .replace(/plan_mode/g, this.plan_mode)
                .replace(/travel_mode/g, this.travel_mode)
                .replace(/dates/g, this.dates);

            if (type === 'rent') {
                if (this.rent_requirements) {
                    this.contents[index].rentCompleted = 1;
                    // 结合用户需求重新定制租车prompt并调用deepseek api进行提问
                    const prompt = promptTemplate + TRAVEL_PLAN_PROMPT_RENT.replace(/customized_rent_prompt/g, this.rent_requirements || '');
                    this.contents[index]['rent'] = '';
                    await this.planningRent(index, user, formattedDateTime, prompt);
                    this.contents[index].rentCompleted = 2;
                    this.rent_customized = true;
                    processed = true;
                }
            } else if (type === 'driving') {
                if (this.plan_requirements) {
                    this.contents[index].drivingCompleted = 1;
                    // 结合路线规划策略重新让deepseek生成路线规划文本信息
                    let driving_s_e_prompt = '';
                    if (index > 0) {
                        driving_s_e_prompt = `注意，第${index - 1}天路线已完成规划，起点是${this.last_start}, 终点是${this.last_end}，因此今天的起点就是${this.last_end}，最终的目的地是${this.e_address}`;
                    }
                    const prompt = promptTemplate + TRAVEL_PLAN_PROMPT_DRIVING
                        .replace(/index/g, index + 1)
                        .replace(/driving_mid_s_e_prompt/g, driving_s_e_prompt)
                        .replace(/customized_driving_prompt/g, this.selectedPlanText || '');
                    this.contents[index]['driving'] = '';
                    const policy = parseInt(this.plan_requirements, 10);
                    await this.planningDriving(index, user, formattedDateTime, prompt, policy);

                    this.contents[index].drivingCompleted = 2;
                    this.plan_customized = true;
                    processed = true;
                }
            } else if (type === 'hotel') {
                if (this.hotel_requirements) {
                    this.contents[index].hotelCompleted = 1;
                    const prompt = promptTemplate + TRAVEL_PLAN_PROMPT_HOTEL
                        .replace(/e_address/g, this.e_address)
                        .replace(/customized_hotel_prompt/g, this.hotel_requirements || '');
                    for (let i = 0; i < this.contents[index]['hotel'].length; i++) {
                        this.contents[index]['hotel'][i]['info'] = '';
                        this.contents[index]['hotel'][i]['name'] = '';
                        this.contents[index]['hotel'][i]['url'] = '';
                    }

                    await this.planningHotel(index, user, formattedDateTime, prompt);
                    this.contents[index].hotelCompleted = 2;
                    this.hotel_customized = true;
                    processed = true;
                }
            }

            if (processed && this.showBtn) {
                await this.savePlanToDB(index, user.account, formattedDateTime);
                // 保存更新后的规划内容
                this.savePlanningContents();
            }
        },
        validateFormData() {
            const formData = {
                s_address: this.s_address,
                e_address: this.e_address,
                startDate: this.startDate,
                dates: this.dates,
                plan_mode: this.plan_mode,
                travel_mode: this.travel_mode,
                s_location: this.s_location,
                e_location: this.e_location
            };

            const validation = formManager.validateFormData(formData);

            if (!validation.isValid) {
                alert(validation.message);
            }

            return validation.isValid;
        },

        // 获取默认表单数据
        getDefaultFormData() {
            return formManager.getDefaultFormData();
        },

        // 获取地点详细信息的摘要
        getLocationSummary(location) {
            if (!location) return '未设置';

            const parts = [];
            if (location.province) parts.push(location.province);
            if (location.city) parts.push(location.city);
            if (location.district) parts.push(location.district);

            return parts.length > 0 ? parts.join(' ') : location.address || '位置信息不完整';
        },

        // 检查是否有完整的位置信息
        hasCompleteLocation(location) {
            return (location && location.lng && location.lat) || (location && location.city && location.province);
        },
        getFormattedDate() {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDate = `${year}${month}${day}`;
            const formattedTime = `${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}${String(date.getSeconds()).padStart(2, '0')}`;
            return `${formattedDate}${formattedTime}`;
        },
        async planningWeather(index, user, formattedDateTime, promptTemplate) {
            this.contents[index].weatherCompleted = 1;
            this.setLoadingState('weather', true);
            const prompt = promptTemplate + TRAVEL_PLAN_PROMPT_WEATHER + TRAVEL_PLAN_PROMPT_SUFFIX;
            this.contents[index]['weather'] = await this.askDS(index, prompt);
            this.contents[index].weatherCompleted = 2;
            this.setLoadingState('weather', false);
        },
        async planningRent(index, user, formattedDateTime, promptTemplate) {
            this.contents[index].rentCompleted = 1;
            this.setLoadingState('rent', true);
            const rent_prompt = TRAVEL_PLAN_PROMPT_RENT.replace(/customized_rent_prompt/g, this.rent_requirements || '');
            const prompt = promptTemplate + rent_prompt + TRAVEL_PLAN_PROMPT_SUFFIX;
            this.contents[index]['rent'] = await this.askDS(index, prompt);
            this.contents[index].rentCompleted = 2;
            this.setLoadingState('rent', false);
        },
        async planningDriving(index, user, formattedDateTime, promptTemplate, policy) {
            this.contents[index].drivingCompleted = 1;
            this.setLoadingState('driving', true);
            let driving_s_e_prompt = '';
            if ((index > 0 && index < this.dates - 1) || (index > 0 && index === this.dates - 1 && this.plan_mode === '单程')) {
                driving_s_e_prompt = `注意，第${index}天路线已完成规划，起点是${this.last_start}, 终点是${this.last_end}，因此今天的起点就是${this.last_end}，最终的目的地是${this.e_address}`;
            } else if (index === this.dates - 1 && this.dates > 1 && this.plan_mode === '往返') {
                driving_s_e_prompt = `注意，第${index}天路线已完成规划，起点是${this.last_start}, 终点是${this.last_end}，因此今天的起点就是${this.last_end}，最终的目的地是${this.s_address}`;
            }

            let circle_prompt = '';
            for (let i = 0; i < this.dates; ++i) {
                if (this.contents[i]['circle']) {
                    for (let j = 0; j < this.contents[i]['circle'].length; j++) {
                        circle_prompt += `${this.contents[i]['circle'][j]},`;
                    }
                }
            }
            if (circle_prompt) {
                circle_prompt = `注意，我已经去过${circle_prompt}这几个地方了，规划环线时不要重复，`;
            }

            const driving_prompt = TRAVEL_PLAN_PROMPT_DRIVING
                .replace(/index/g, index + 1)
                .replace(/driving_mid_s_e_prompt/g, driving_s_e_prompt)
                .replace(/last_circle_prompt/g, circle_prompt)
                .replace(/customized_driving_prompt/g, this.selectedPlanText || '');
            const prompt = promptTemplate + driving_prompt + TRAVEL_PLAN_PROMPT_SUFFIX;

            // 需要剔除路线规划中的一些内部信息，不需要展示给用户
            // 路线规划中的内部信息：^^、%%、SP、SC、EP、EC
            const driving_result = await this.askDS(index, prompt);
            this.contents[index]['driving'] = driving_result
                .replace(/%SP/g, '').replace(/%SC/g, '').replace(/%EP/g, '').replace(/%EC/g, '')
                .replace(/%CIR/g, '')
                .replace(/\^/g, '')
                .replace(/%/g, '');
            // 起点信息
            const driving_s_address = driving_result.split('^^')[1].split('^^')[0].trim();
            const driving_s_province = driving_result.split('%SP')[1].split('%SP')[0].trim();
            const driving_s_city = driving_result.split('%SC')[1].split('%SC')[0].trim();

            // 终点信息
            const driving_e_address = driving_result.split('%%')[1].split('%%')[0].trim();
            const driving_e_province = driving_result.split('%EP')[1].split('%EP')[0].trim();
            const driving_e_city = driving_result.split('%EC')[1].split('%EC')[0].trim();

            this.last_start = driving_s_address;
            // 记录上一次的终点信息，这是明天的起点（如果有明天的话）
            this.last_end = driving_e_address;

            // 使用局部变量存储经纬度信息，避免污染全局s_location和e_location
            let currentDayStartLocation = { lng: null, lat: null };
            let currentDayEndLocation = { lng: null, lat: null };

            // 通过高德地图地理编码API获取起点和终点的经纬度信息
            const startLocationInfo = await mapService.getAccurateCoordinates(driving_s_address, driving_s_province, driving_s_city);
            const endLocationInfo = await mapService.getAccurateCoordinates(driving_e_address, driving_e_province, driving_e_city);

            // 检查地理编码结果，如果失败则使用默认坐标或跳过该天的地图规划
            if (!startLocationInfo.lng || !startLocationInfo.lat) {
                currentDayStartLocation = { lng: null, lat: null };
            } else {
                currentDayStartLocation = { lng: startLocationInfo.lng, lat: startLocationInfo.lat };
            }

            if (!endLocationInfo.lng || !endLocationInfo.lat) {
                currentDayEndLocation = { lng: null, lat: null };
            } else {
                currentDayEndLocation = { lng: endLocationInfo.lng, lat: endLocationInfo.lat };
            }

            // 环线处理
            let circleLocations = [];
            if (driving_s_address === driving_e_address) {
                const circlePlan = driving_result.split('%CIR')[1];
                const circlePos = circlePlan.split(' → ');
                this.contents[index]['circle'] = []

                // 剔除起点终点，它们在 driving_s_address和 driving_e_address 中
                for (let i = 1; i < circlePos.length - 1; ++i) {
                    const pos = circlePos[i].trim();
                    const posInfo = await mapService.getAccurateCoordinates(pos, driving_e_province, driving_e_city);
                    // 记录途径点，避免重复规划
                    this.contents[index]['circle'].push(pos);

                    // 检查环线地点地理编码结果
                    if (posInfo && posInfo.lng && posInfo.lat) {
                        circleLocations.push([posInfo.lng, posInfo.lat]);
                    } else {
                        console.warn(`环线地点地理编码失败: ${pos}，跳过该地点`);
                    }
                }
            }

            // 将今日 DS 规划的起点和终点信息保存下来，用于环线地图的恢复
            this.contents[index]['pos'] = { 's_address': driving_s_address, 's_province': driving_s_province, 's_city': driving_s_city, 'e_address': driving_e_address, 'e_province': driving_e_province, 'e_city': driving_e_city, 's_location': { 's_lng': currentDayStartLocation.lng, 's_lat': currentDayStartLocation.lat }, 'e_location': { 'e_lng': currentDayEndLocation.lng, 'e_lat': currentDayEndLocation.lat }, 'circle_locations': circleLocations };

            // 进行高德路线规划
            this.contents[index]['amap'] = '';

            // 检查是否有有效的起终点坐标
            if (currentDayStartLocation.lng && currentDayStartLocation.lat &&
                currentDayEndLocation.lng && currentDayEndLocation.lat) {

                // 等待地图容器加载
                await this.$nextTick();
                await this.$nextTick();

                try {
                    // 使用当前天的局部坐标进行路线规划
                    await this.drivingPlanning(index,
                        { s_lng: currentDayStartLocation.lng, s_lat: currentDayStartLocation.lat },
                        { e_lng: currentDayEndLocation.lng, e_lat: currentDayEndLocation.lat },
                        policy,
                        circleLocations
                    );

                    // 路线规划完成后，额外等待确保地图完全渲染
                    await new Promise(resolve => setTimeout(resolve, 1000));
                } catch (err) {
                    log.error(`第${index + 1}天导航规划失败:`, err);
                }
            } else {
                console.warn(`第${index + 1}天地理编码失败，跳过地图规划，但继续其他规划流程`);
            }

            this.contents[index].drivingCompleted = 2;
            this.setLoadingState('driving', false);
        },
        async planningView(index, user, formattedDateTime, promptTemplate) {
            this.contents[index].viewCompleted = 1;
            this.setLoadingState('view', true);
            this.contents[index]['view'] = [];
            let view_prompt = TRAVEL_PLAN_PROMPT_VIEW.replace(/e_address/g, this.last_end);
            if (index > 0) {
                let views = '';
                for (let i = 0; i < index; ++i) {
                    for (let j = 0; j < this.contents[i]['view'].length; ++j) {
                        views += `${this.contents[i]['view'][j]['name']},`
                    }
                }
                view_prompt = view_prompt.replace(/last_view_prompt/g, `注意，我已经去过${views}这几个地方了，不要重复，`);
            }
            const prompt = promptTemplate + view_prompt + TRAVEL_PLAN_PROMPT_SUFFIX;
            const result = await this.askDS(index, prompt);
            const view_parts = result.split('@@@@');
            for (let i = 0; i < view_parts.length; i++) {
                if (i === 0) continue;
                if (i > this.contents[index]['view'].length) this.contents[index]['view'].push({});
                if (view_parts[i].includes('^^')) {
                    this.contents[index]['view'][i - 1]['name'] = view_parts[i].split('^^')[1].split('^^')[0].trim();
                } else if (view_parts[i].includes('$$')) {
                    this.contents[index]['view'][i - 1]['name'] = view_parts[i].split('$$')[1].split('$$')[0].trim();
                }
                this.contents[index]['view'][i - 1]['info'] = view_parts[i]
                    .replace(/\^\^.*\^\^/g, '')
                    .replace(/\$\$.*\$\$/g, '')
                    .replace(/%PT/g, '')
                    .trim();
                this.contents[index]['view'][i - 1]['prompt'] = view_parts[i].split('%PT')[1].split('%PT')[0].trim();
                try {
                    this.contents[index]['view'][i - 1]['url'] = await apiService.getAIImg(this.contents[index]['view'][i - 1]['name'], this.contents[index]['view'][i - 1]['prompt']);
                } catch (err) {
                    this.contents[index]['view'][i - 1]['url'] = '';
                }
            }
            this.contents[index].viewCompleted = 2;
            this.setLoadingState('view', false);
        },
        async planningFood(index, user, formattedDateTime, promptTemplate) {
            this.contents[index].foodCompleted = 1;
            this.setLoadingState('food', true);
            this.contents[index]['food'] = [];
            let food_prompt = TRAVEL_PLAN_PROMPT_FOOD.replace(/e_address/g, this.last_end);
            if (index > 0) {
                let foods = '';
                for (let i = 0; i < index; ++i) {
                    for (let j = 0; j < this.contents[i]['food'].length; ++j) {
                        foods += `${this.contents[i]['food'][j]['name']},`
                    }
                }
                food_prompt = food_prompt.replace(/last_food_prompt/g, `注意，我已经吃过${foods}这几个美食了，不要重复，`);
            }
            const prompt = promptTemplate + food_prompt + TRAVEL_PLAN_PROMPT_SUFFIX;
            const result = await this.askDS(index, prompt);
            const food_parts = result.split('@@@@');
            for (let i = 0; i < food_parts.length; i++) {
                if (i === 0) continue;
                if (i > this.contents[index]['food'].length) this.contents[index]['food'].push({});
                if (food_parts[i].includes('^^')) {
                    this.contents[index]['food'][i - 1]['name'] = food_parts[i].split('^^')[1].split('^^')[0].trim();
                } else if (food_parts[i].includes('$$')) {
                    this.contents[index]['food'][i - 1]['name'] = food_parts[i].split('$$')[1].split('$$')[0].trim();
                }
                this.contents[index]['food'][i - 1]['info'] = food_parts[i]
                    .replace(/\^/g, '')
                    .replace(/\$/g, '')
                    .replace(/%PT/g, '')
                    .trim();
                this.contents[index]['food'][i - 1]['prompt'] = food_parts[i].split('%PT')[1].split('%PT')[0].trim();
                try {
                    // this.contents[index]['food'][i - 1]['url'] = await this.getFoodImgUrl(this.contents[index]['food'][i - 1]['name'], this.contents[index]['food'][i - 1]['info']);
                    this.contents[index]['food'][i - 1]['url'] = await apiService.getAIImg(this.contents[index]['food'][i - 1]['name'], this.contents[index]['food'][i - 1]['prompt']);
                } catch (err) {
                    this.contents[index]['food'][i - 1]['url'] = '';
                }
            }
            this.contents[index].foodCompleted = 2;
            this.setLoadingState('food', false);
        },
        async planningHotel(index, user, formattedDateTime, promptTemplate) {
            this.contents[index].hotelCompleted = 1;
            this.setLoadingState('hotel', true);
            this.contents[index]['hotel'] = [];
            const hotel_prompt = TRAVEL_PLAN_PROMPT_HOTEL
                .replace(/e_address/g, this.last_end)
                .replace(/customized_hotel_prompt/g, this.hotel_requirements || '');
            const prompt = promptTemplate + hotel_prompt + TRAVEL_PLAN_PROMPT_SUFFIX;
            const result = await this.askDS(index, prompt);
            const hotel_parts = result.split('@@@@');
            for (let i = 0; i < hotel_parts.length; i++) {
                if (i === 0) continue;
                if (i > this.contents[index]['hotel'].length) this.contents[index]['hotel'].push({});
                this.contents[index]['hotel'][i - 1]['name'] = hotel_parts[i].split('^^')[1].split('^^')[0].trim();
                this.contents[index]['hotel'][i - 1]['info'] = hotel_parts[i]
                    .replace(/\^\^.*\^\^/g, '')
                    .replace(/\$\$.*\$\$/g, '')
                    .trim();
                try {
                    this.contents[index]['hotel'][i - 1]['url'] = await this.getHotelUrl(this.contents[index]['hotel'][i - 1]['name'], user.account, formattedDateTime, i);
                } catch (err) {
                    this.contents[index]['hotel'][i - 1]['url'] = 'https://www.ctrip.com';
                }
            }
            this.contents[index].hotelCompleted = 2;
            this.setLoadingState('hotel', false);
        },
        async planningCost(index, user, formattedDateTime, promptTemplate) {
            this.contents[index].costCompleted = 1;
            this.setLoadingState('cost', true);
            const prompt = promptTemplate + TRAVEL_PLAN_PROMPT_COST + TRAVEL_PLAN_PROMPT_SUFFIX;
            this.contents[index]['cost'] = await this.askDS(index, prompt);
            this.contents[index].costCompleted = 2;
            this.setLoadingState('cost', false);
        },
        async planningNew() {
            // 校验用户是否登录
            const us = useUserStore();
            if (!us.checkLoginStatus()) {
                alert('请先登录');
                return;
            }
            const { user } = await us.getUserInfo();

            // 检查是否有未完成的规划
            if (this.hasSavedPlanningContents()) {
                let shouldContinue = false;
                if (this.shouldContinuePlanning()) {
                    shouldContinue = confirm('检测到有未完成的规划内容，是否继续之前的规划？\n\n点击"确定"继续之前的规划\n点击"取消"开始新的规划');
                }
                if (shouldContinue) {
                    // 加载并继续之前的规划
                    const hasRestored = this.loadPlanningContents();
                    if (hasRestored) {
                        await this.$nextTick();
                        setTimeout(() => {
                            this.handleContentRestoration(false);
                        }, 500);
                    }
                }
            }

            // 数据校验
            if (!this.validateFormData()) {
                return;
            }

            // 起始地点必须通过高德地点列表进行选择得出地点的经纬度信息，否则路线规划可能是错误的
            if (!this.hasCompleteLocation(this.s_location)) {
                alert('为避免地点位置错误，请根据弹出的地点信息选择起点');
                return;
            }
            if (!this.hasCompleteLocation(this.e_location)) {
                alert('为避免地点位置错误，请根据弹出的地点信息选择终点');
                return;
            }

            // 隐藏按钮
            this.showBtn = false;

            this.last_start = this.s_address;
            this.last_end = this.e_address;

            // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
            this.resetScrollState();

            // 清除旧的规划内容
            this.clearPlanningContents();

            // 状态初始化
            this.contents = [];

            // 为每一天初始化contents对象
            for (let i = 0; i < this.dates; i++) {
                // 规划状态：0-未开始，1-进行中，2-已完成
                this.contents.push({
                    weatherCompleted: 0,
                    rentCompleted: 0,
                    drivingCompleted: 0,
                    viewCompleted: 0,
                    hotelCompleted: 0,
                    foodCompleted: 0,
                    costCompleted: 0
                });
            }

            // 获取时间
            const formattedDateTime = this.getFormattedDate();

            // 前置 prompt 准备
            let promptTemplate = TRAVEL_PLAN_PROMPT_PREFIX;
            promptTemplate = promptTemplate.replace(/startDate/g, this.startDate)
                .replace(/s_address/g, this.s_address)
                .replace(/e_address/g, this.e_address)
                .replace(/plan_mode/g, this.plan_mode)
                .replace(/travel_mode/g, this.travel_mode)
                .replace(/dates/g, this.dates);

            // 天气、租车信息，只显示一次即可
            // 天气信息
            try {
                await this.planningWeather(0, user, formattedDateTime, promptTemplate);
            } catch (err) {
                log.error(`天气规划失败：${err}`);
                this.planningFinishProc();
                // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
                this.resetScrollState();
                // 清空规划内容
                this.contents = [];
                // 显示规划失败窗口
                this.showPlanningFailure('天气信息获取失败，请检查网络连接后重试');
                return;
            }

            // 租车信息（可选）
            if (this.travel_mode === '租车') {
                try {
                    await this.planningRent(0, user, formattedDateTime, promptTemplate);
                } catch (err) {
                    log.error(`租车方案规划失败：${err}`);
                    this.planningFinishProc();
                    // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
                    this.resetScrollState();
                    // 清空规划内容
                    this.contents = [];
                    // 显示规划失败窗口
                    this.showPlanningFailure('租车方案规划失败，请检查网络连接后重试');
                    return;
                }
            }

            for (let index = 0; index < this.dates; index++) {
                // 路线规划
                try {
                    await this.planningDriving(index, user, formattedDateTime, promptTemplate);
                } catch (err) {
                    log.error(`路线规划失败：${err}`);
                    this.planningFinishProc();
                    // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
                    this.resetScrollState();
                    // 清空规划内容
                    this.contents = [];
                    // 显示规划失败窗口
                    this.showPlanningFailure('路线规划失败，请检查网络连接后重试');
                    return;
                }

                // 单程或返程的非最后一天才需要规划景点、美食、住宿等内容
                if ((index !== this.dates - 1 || index === 0) || this.plan_mode === '单程') {
                    // 景点信息
                    try {
                        await this.planningView(index, user, formattedDateTime, promptTemplate);
                    } catch (err) {
                        log.error(`景点规划失败：${err}`);
                        this.planningFinishProc();
                        // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
                        this.resetScrollState();
                        // 清空规划内容
                        this.contents = [];
                        // 显示规划失败窗口
                        this.showPlanningFailure('景点推荐规划失败，请检查网络连接后重试');
                        return;
                    }

                    // 美食信息
                    try {
                        await this.planningFood(index, user, formattedDateTime, promptTemplate);
                    } catch (err) {
                        log.error(`美食规划失败：${err}`);
                        this.planningFinishProc();
                        // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
                        this.resetScrollState();
                        // 清空规划内容
                        this.contents = [];
                        // 显示规划失败窗口
                        this.showPlanningFailure('美食推荐规划失败，请检查网络连接后重试');
                        return;
                    }

                    // 住宿信息
                    try {
                        await this.planningHotel(index, user, formattedDateTime, promptTemplate);
                    } catch (err) {
                        log.error(`住宿规划失败：${err}`);
                        this.planningFinishProc();
                        // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
                        this.resetScrollState();
                        // 清空规划内容
                        this.contents = [];
                        // 显示规划失败窗口
                        this.showPlanningFailure('住宿推荐规划失败，请检查网络连接后重试');
                        return;
                    }
                }

                // 费用信息
                // await this.planningCost(index, user, formattedDateTime, promptTemplate);

                // 存库
                try {
                    await this.savePlanToDB(index, user.account, formattedDateTime);
                } catch (err) {
                    log.error(`旅游规划存库失败：${err}`);
                }
            }

            await this.planningFinishProc();

            // 显示完成提示窗口
            this.showCompletionModal = true;
        },
        async planningFinishProc() {
            this.showBtn = true;

            // 全部规划完成后的处理
            this.selectedDayIndex = 0; // 设置显示第一天内容
            await this.$nextTick(); // 等待DOM更新
            this.refreshVisibleMaps(); // 刷新地图显示
        },
        // 处理完成提示窗口确认按钮
        handleCompletionModalConfirm() {
            this.showCompletionModal = false;

            // 滚动到导航天数按钮位置
            this.$nextTick(() => {
                const dayNavigation = document.querySelector('.day-navigation');
                if (dayNavigation) {
                    // 计算滚动目标位置：导航按钮距离页面顶部留出一些空间
                    const targetPosition = dayNavigation.offsetTop - 100;

                    window.scrollTo({
                        top: Math.max(0, targetPosition),
                        behavior: 'smooth'
                    });
                }
            });
        },
        // 处理失败提示窗口确认按钮
        handleFailureModalConfirm() {
            this.showFailureModal = false;
        },
        // 显示规划失败窗口
        showPlanningFailure(reason) {
            this.failureReason = reason;
            this.showFailureModal = true;
        },

        // 悬浮滚动按钮相关方法
        initFloatingButtons() {
            if (typeof window === 'undefined') return;

            this.handleScroll = this.throttle(() => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const scrollHeight = document.documentElement.scrollHeight;
                const clientHeight = document.documentElement.clientHeight;

                // 当页面滚动超过200px且页面高度足够时显示按钮
                this.showFloatingButtons = scrollTop > 200 && scrollHeight > clientHeight + 400;
            }, 100);

            window.addEventListener('scroll', this.handleScroll, { passive: true });
        },

        scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        },

        scrollToBottom() {
            window.scrollTo({
                top: document.documentElement.scrollHeight,
                behavior: 'smooth'
            });
        },

        // 节流函数
        throttle(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func.apply(this, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // 初始化主题观察器
        initThemeObserver() {
            if (typeof window === 'undefined') return;

            // 监听主题变化
            this.themeObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        // 主题发生变化，更新自定义地址建议样式
                        this.updateCustomSuggestTheme();
                    }
                });
            });

            this.themeObserver.observe(document.documentElement, {
                attributes: true,
                attributeFilter: ['class']
            });
        },

        // 更新自定义地址建议的主题样式
        updateCustomSuggestTheme() {
            const containers = document.querySelectorAll('.custom-amap-suggest');
            containers.forEach(container => {
                if (mapService.updateThemeStyles) {
                    mapService.updateThemeStyles(container);
                }
            });
        },

        // 处理内容恢复后的完整恢复流程
        async handleContentRestoration(doContinueCheck = true) {
            try {
                // 1. 重新初始化地图
                await this.restoreMapInstances();

                // 2. 恢复滚动管理器状态
                this.restoreScrollManagerState();

                // 3. 检查是否需要继续规划
                let shouldContinue = false;
                if (doContinueCheck) {
                    shouldContinue = this.shouldContinuePlanning();
                    if (shouldContinue) {
                        await this.continuePlanning();
                    }
                }
                if (!doContinueCheck || !shouldContinue) {
                    // 如果规划已完成，恢复完成状态
                    this.restoreCompletedState();
                    await this.planningFinishProc();
                    // 显示完成提示窗口
                    this.showCompletionModal = true;
                }
            } catch (error) {
                // 恢复失败时，清除数据并重置状态
                this.clearPlanningContents();
                this.contents = [];
                this.selectedDayIndex = -1;
                this.showBtn = true;

                // 重置所有加载状态
                this.resetAllLoadingStates();

                // 显示错误提示
                alert('恢复规划内容失败，请重新开始规划');
            }
        },

        // 重新初始化地图实例
        async restoreMapInstances() {
            if (!this.contents || this.contents.length === 0) return;

            // 等待DOM完全准备
            await this.$nextTick();
            await this.$nextTick();

            // 确保地图服务已初始化
            try {
                await mapService.initialize();
            } catch (error) {
                console.warn('地图服务重新初始化失败:', error);
            }

            // 遍历所有包含地图的内容
            for (let index = 0; index < this.contents.length; index++) {
                const content = this.contents[index];

                // 如果这一天有路线规划（driving）
                if (content.drivingCompleted === 2) {
                    try {
                        // 等待一定时间确保前一个地图已处理完成
                        await new Promise(resolve => setTimeout(resolve, 200 * index));

                        // 检查地图容器是否存在
                        const mapContainer = document.querySelector(`#map-container-${index}`);
                        if (mapContainer) {
                            // 先清空地图容器，强制重新创建
                            mapContainer.innerHTML = '';

                            // 等待容器清空
                            await this.$nextTick();

                            // 重新生成地图
                            await this.regenerateMapRoute(index);
                        } else {
                            console.warn(`第${index + 1}天地图容器不存在`);
                        }
                    } catch (error) {
                        console.warn(`地图 ${index + 1} 恢复失败:`, error);
                    }
                }
            }

            // 最后刷新所有可见地图
            setTimeout(() => {
                this.refreshVisibleMaps();

                // 如果地图还是有问题，再强制刷新一次
                setTimeout(() => {
                    this.forceRefreshAllMaps();
                }, 3000);
            }, 2000);
        },

        // 重新生成地图路线
        async regenerateMapRoute(index) {
            try {
                const content = this.contents[index];
                if (!content || !content.driving) {
                    console.warn(`第${index + 1}天没有路线规划数据`);
                    return;
                }

                // 如果有有效的起终点坐标，重新生成路线
                if (this.contents[index]['pos']['s_location'] && this.contents[index]['pos']['e_location'] &&
                    this.contents[index]['pos']['s_location'].s_lng && this.contents[index]['pos']['s_location'].s_lat &&
                    this.contents[index]['pos']['e_location'].e_lng && this.contents[index]['pos']['e_location'].e_lat) {

                    const policy = parseInt(this.plan_requirements || '0', 10);

                    // 等待地图容器完全准备
                    await this.$nextTick();
                    await this.$nextTick();
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // 重新生成路线
                    await this.drivingPlanning(index, this.contents[index]['pos']['s_location'], this.contents[index]['pos']['e_location'], policy, this.contents[index]['pos']['circle_locations']);

                    // 确保地图正确显示
                    setTimeout(() => {
                        this.forceMapResize(index);
                    }, 1000);
                } else {
                    console.warn(`第${index + 1}天坐标信息无效，无法生成地图`);
                }
            } catch (error) {
                console.error(`第${index + 1}天地图路线重新生成失败:`, error);

                // 如果重新生成失败，尝试基本的地图显示恢复
                setTimeout(() => {
                    try {
                        // 检查地图容器
                        const mapContainer = document.querySelector(`#map-container-${index}`);
                        if (mapContainer) {
                            // 尝试基本的地图resize
                            this.forceMapResize(index);
                        }
                    } catch (resizeError) {
                        console.error(`第${index + 1}天地图resize失败:`, resizeError);
                    }
                }, 1000);
            }
        },

        // 恢复滚动管理器状态
        restoreScrollManagerState() {
            // 重置滚动状态以启用自动滚动
            scrollManager.resetScrollState();
        },

        // 检查是否需要继续规划
        shouldContinuePlanning() {
            if (!this.contents || this.contents.length === 0) return false;

            // 检查是否有未完成的规划
            for (let index = 0; index < this.contents.length; index++) {
                const content = this.contents[index];

                // 检查每个阶段的完成状态
                const stages = [
                    'weatherCompleted',
                    'rentCompleted',
                    'drivingCompleted',
                    'viewCompleted',
                    'foodCompleted',
                    'hotelCompleted',
                    'costCompleted'
                ];

                for (const stage of stages) {
                    // 如果有阶段状态为1（进行中），说明被中断了，需要继续
                    if (content[stage] === 1) {
                        return true;
                    }
                }

                // 检查是否有应该完成但未开始的阶段
                if (this.hasIncompleteStages(content, index)) {
                    return true;
                }
            }

            return false;
        },

        // 检查是否有应该完成但未开始的阶段
        hasIncompleteStages(content, index) {
            // 天气信息（只在第一天）
            if (index === 0 && content.weatherCompleted === 0) {
                return true;
            }

            // 租车信息（只在第一天且旅行模式为租车）
            if (index === 0 && this.travel_mode === '租车' && content.rentCompleted === 0) {
                return true;
            }

            // 路线规划（每天都需要）
            if (content.drivingCompleted === 0) {
                return true;
            }

            // 景点、美食、住宿（除了往返最后一天）
            const needsViewFoodHotel = (index !== this.dates - 1 || index === 0) || this.plan_mode === '单程';
            if (needsViewFoodHotel) {
                if (content.viewCompleted === 0 || content.foodCompleted === 0 || content.hotelCompleted === 0) {
                    return true;
                }
            }

            return false;
        },

        // 继续规划
        async continuePlanning() {
            try {
                // 获取用户信息
                const us = useUserStore();
                const { user } = await us.getUserInfo();
                const formattedDateTime = this.getFormattedDate();

                // 准备提示模板
                let promptTemplate = TRAVEL_PLAN_PROMPT_PREFIX;
                promptTemplate = promptTemplate.replace(/startDate/g, this.startDate)
                    .replace(/s_address/g, this.s_address)
                    .replace(/e_address/g, this.e_address)
                    .replace(/plan_mode/g, this.plan_mode)
                    .replace(/travel_mode/g, this.travel_mode)
                    .replace(/dates/g, this.dates);

                // 继续未完成的规划
                for (let index = 0; index < this.contents.length; index++) {
                    const content = this.contents[index];

                    // 天气信息
                    if (index === 0 && content.weatherCompleted !== 2) {
                        try {
                            await this.planningWeather(index, user, formattedDateTime, promptTemplate);
                        } catch (err) {
                            console.warn(`第${index + 1}天天气规划失败:`, err);
                            throw err;
                        }
                    }

                    // 租车信息
                    if (index === 0 && this.travel_mode === '租车' && content.rentCompleted !== 2) {
                        try {
                            await this.planningRent(index, user, formattedDateTime, promptTemplate);
                        } catch (err) {
                            console.warn(`第${index + 1}天租车规划失败:`, err);
                            throw err;
                        }
                    }

                    // 路线规划
                    if (content.drivingCompleted !== 2) {
                        const policy = parseInt(this.plan_requirements, 10);
                        try {
                            await this.planningDriving(index, user, formattedDateTime, promptTemplate, policy);
                        } catch (err) {
                            console.warn(`第${index + 1}天路线规划失败:`, err);
                            throw err;
                        }
                    }

                    // 检查是否需要景点、美食、住宿规划
                    const needsViewFoodHotel = (index !== this.dates - 1 || index === 0) || this.plan_mode === '单程';
                    if (needsViewFoodHotel) {
                        // 景点信息
                        if (content.viewCompleted !== 2) {
                            try {
                                await this.planningView(index, user, formattedDateTime, promptTemplate);
                            } catch (err) {
                                console.warn(`第${index + 1}天景点规划失败:`, err);
                                throw err;
                            }
                        }

                        // 美食信息
                        if (content.foodCompleted !== 2) {
                            try {
                                await this.planningFood(index, user, formattedDateTime, promptTemplate);
                            } catch (err) {
                                console.warn(`第${index + 1}天美食规划失败:`, err);
                                throw err;
                            }
                        }

                        // 住宿信息
                        if (content.hotelCompleted !== 2) {
                            try {
                                await this.planningHotel(index, user, formattedDateTime, promptTemplate);
                            } catch (err) {
                                console.warn(`第${index + 1}天住宿规划失败:`, err);
                                throw err;
                            }
                        }
                    }

                    // 存库
                    try {
                        await this.savePlanToDB(index, user.account, formattedDateTime);
                    } catch (err) {
                        console.warn(`第${index + 1}天存库失败:`, err);
                    }
                }

                // 完成规划
                await this.planningFinishProc();

                // 显示完成提示窗口
                this.showCompletionModal = true;

            } catch (error) {
                console.error('继续规划失败:', error);

                // 重置所有加载状态
                this.resetAllLoadingStates();

                // 重置按钮状态
                this.showBtn = true;

                this.showPlanningFailure('继续规划失败，请检查网络连接后重试');
            }
        },

        // 恢复已完成状态
        restoreCompletedState() {
            // 设置为已完成状态
            this.showBtn = true;

            // 重置所有加载状态
            this.resetAllLoadingStates();

            // 如果有内容，默认显示第一天
            if (this.contents.length > 0) {
                this.selectedDayIndex = 0;

                // 刷新地图显示
                this.$nextTick(() => {
                    // 延迟刷新地图，确保DOM完全渲染
                    setTimeout(() => {
                        this.refreshVisibleMaps();

                        // 额外检查地图状态
                        this.contents.forEach((content, index) => {
                            if (content.drivingCompleted === 2) {
                                setTimeout(() => {
                                    this.forceMapResize(index);
                                }, 500 * (index + 1));
                            }
                        });
                    }, 1000);
                });
            }
        },

        // 强制刷新所有地图（紧急修复方法）
        forceRefreshAllMaps() {
            this.contents.forEach((content, index) => {
                if (content.drivingCompleted === 2) {
                    setTimeout(() => {
                        try {
                            const mapContainer = document.querySelector(`#map-container-${index}`);
                            if (mapContainer) {
                                // 检查地图实例是否存在
                                const mapInstance = mapService.getMapInstance(index);
                                if (!mapInstance) {
                                    this.regenerateMapRoute(index);
                                } else {
                                    this.forceMapResize(index);
                                }
                            }
                        } catch (error) {
                            console.error(`强制刷新第${index + 1}天地图失败:`, error);
                        }
                    }, 1000 * (index + 1));
                }
            });
        }
    }
}